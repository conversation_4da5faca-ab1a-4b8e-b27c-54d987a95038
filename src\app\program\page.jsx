'use client';

import Image from 'next/image';
import React from 'react';

import AdvancedSEO from '@/components/SEO/AdvancedSEO';
import { getProgramByDestination, getAllDestinations, destinations } from '../../data/programData';
import { motion } from 'framer-motion';

import { generateRetreatStructuredData } from '@/lib/advancedSEO';

const generatePageMetadata = (destination) => {
  const dest = destinations[destination] || destinations.bali;
  return {
    title: `${dest.name} Retreaty Jogi 2025 - Transformacyjne Podróże | BAKASANA`,
    description: `Odkryj najlepsze retreaty jogi na ${dest.name} z certyfikowaną instruktorką Julią Jakubowicz. ${dest.seoDescription || 'Autentyczne doświadczenia duchowe w rajskim otoczeniu.'}`,
    keywords: dest.seoKeywords || [
      `retreat jogi ${dest.name.toLowerCase()}`,
      'transformacyjne podróże',
      'ducho<PERSON> waka<PERSON>',
      'julia j<PERSON><PERSON> joga',
      'małe grupy retreat',
      'luksusowe hotele joga'
    ],
    structuredData: generateRetreatStructuredData(dest),
    canonicalUrl: `https://bakasana-travel.blog/program?destination=${destination}`,
    imageUrl: `https://bakasana-travel.blog${dest.image}`,
  };
};

export default async function ProgramPage({ searchParams }) {
  // Get destination from URL params, default to 'bali'
  const params = await searchParams;
  const selectedDestination = params?.destination || 'bali';
  const currentDestination = destinations[selectedDestination] || destinations.bali;
  const programDays = getProgramByDestination(selectedDestination);
  const allDestinations = getAllDestinations();
  const pageMetadata = generatePageMetadata(selectedDestination);

  const includedServices = [
    'Zakwaterowanie w komfortowych hotelach',
    'Codzienne praktyki jogi (poranne i wieczorne)',
    'Śniadania w hotelach',
    'Transfery między lokalizacjami',
    'Opieka polskojęzycznego przewodnika',
    'Ubezpieczenie grupowe',
    'Wsparcie 24/7 podczas całego pobytu'
  ];

  const additionalCosts = [
    'Bilety lotnicze (ok. 2500-3500 zł)',
    'Obiady i kolacje (ok. 15-30 zł za posiłek)',
    'Wiza do Indonezji (35 USD)',
    'Wycieczki fakultatywne',
    'Huśtawka nad tarasami ryżowymi (ok. 15 USD)',
    'Wschód słońca na wulkanie (ok. 50 USD)'
  ];

  return (
    <main className="relative min-h-screen bg-sanctuary">
      {/* Advanced SEO Component */}
      <AdvancedSEO
        title={pageMetadata.title}
        description={pageMetadata.description}
        keywords={pageMetadata.keywords}
        structuredData={[pageMetadata.structuredData]}
        canonicalUrl={pageMetadata.canonicalUrl}
        imageUrl={pageMetadata.imageUrl}
      />

      {/* Enhanced Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-sanctuary via-sanctuary to-warm-sanctuary">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div 
            className="absolute w-96 h-96 bg-terra-lotus/5 blur-3xl"
            style={{ top: '10%', left: '10%' }}
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div 
            className="absolute w-80 h-80 bg-charcoal/5 blur-3xl"
            style={{ top: '60%', right: '15%' }}
            animate={{ 
              scale: [1, 1.1, 1],
              opacity: [0.4, 0.6, 0.4]
            }}
            transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 2 }}
          />
        </div>

        {/* Parallax Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src={currentDestination.image}
            alt={`Retreat jogi ${currentDestination.name}`}
            fill
            className="object-cover object-center opacity-20"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-sanctuary/40 via-transparent to-sanctuary/60" />
        </div>

        {/* Main Content */}
        <div className="relative z-20 text-center max-w-6xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="space-y-lg"
          >
            {/* Om Symbol */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 1 }}
              className="text-terra-lotus mb-lg"
            >
              <svg className="w-16 h-16 mx-auto" viewBox="0 0 100 100" fill="currentColor">
                <path d="M50 20c-5.5 0-10 4.5-10 10s4.5 10 10 10 10-4.5 10-10-4.5-10-10-10zm0 25c-8.3 0-15 6.7-15 15s6.7 15 15 15 15-6.7 15-15-6.7-15-15-15zm0 35c-11 0-20-9-20-20s9-20 20-20 20 9 20 20-9 20-20 20z" />
              </svg>
            </motion.div>

            {/* Main Title */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 1.2 }}
              className="heading-hero text-charcoal mb-md"
            >
              RETREATY {currentDestination.name.toUpperCase()}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 1.4 }}
              className="text-xl md:text-2xl text-charcoal/80 font-primary italic mb-lg /* TODO: Replace with SectionTitle */ /* TODO: Replace with CardTitle */"
            >
              — {currentDestination.tagline || 'transformacyjne podróże ducha'} —
            </motion.p>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 1.6 }}
              className="text-lg md:text-xl text-charcoal/70 max-w-3xl mx-auto leading-relaxed mb-xl"
            >
              {currentDestination.heroDescription || currentDestination.description}
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 1.8 }}
              className="flex flex-col sm:flex-row gap-md justify-center items-center"
            >
              <motion.a
                href="#programs"
                className="btn-primary text-lg px-hero-padding py-4 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  if (window.trackRetreatInterest) {
                    window.trackRetreatInterest('hero_cta', {
                      destination: currentDestination.name,
                      action: 'view_program'
                    });
                  }
                }}
              >
                Zobacz Program
                <svg className="w-5 h-5 ml-2 group-hover:translate-y-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </motion.a>
              
              <motion.a
                href="/rezerwacja"
                className="btn-secondary text-lg px-hero-padding py-4 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  if (window.trackRetreatBooking) {
                    window.trackRetreatBooking(currentDestination.name, 'hero_interest', 1);
                  }
                }}
              >
                Rezerwuj Teraz
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </motion.a>
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 2 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        >
          <div className="flex flex-col items-center text-charcoal/60">
            <span className="text-sm mb-2 font-secondary">Sprawdź szczegóły</span>
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Enhanced Programs Section */}
      <section id="programs" className="py-section px-container-sm sm:px-hero-padding lg:px-hero-padding bg-gradient-to-b from-sanctuary to-warm-sanctuary">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <div className="w-24 h-px bg-terra-lotus/40 mx-auto mb-lg"></div>
            <h2 className="heading-section text-charcoal mb-lg">
              Wybierz Swoją Duchową Podróż
            </h2>
            <p className="text-lg text-charcoal/70 max-w-3xl mx-auto leading-relaxed">
              Szczegółowy harmonogram {currentDestination.duration} retreatu jogowego na {currentDestination.name}
            </p>
          </motion.div>

          {/* Enhanced Destination Selector */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-md mb-20 max-w-4xl mx-auto"
          >
            {allDestinations.map((dest, index) => (
              <motion.a
                key={dest.id}
                href={`/program?destination=${dest.id}`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.98 }}
                className={`group relative overflow-hidden p-8 text-center transition-all duration-500 ${
                  selectedDestination === dest.id
                    ? 'bg-gradient-to-br from-golden-lotus to-temple text-sanctuary shadow-xl'
                    : 'bg-sanctuary/80 backdrop-blur-sm text-charcoal hover:bg-terra-lotus/10 border border-terra-lotus/20'
                }`}
                onClick={() => {
                  if (window.trackRetreatInterest) {
                    window.trackRetreatInterest('destination_selection', {
                      destination: dest.name,
                      from: currentDestination.name
                    });
                  }
                }}
              >
                {/* Background decoration */}
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-golden-lotus/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                
                <div className="relative z-10">
                  <div className="text-2xl font-primary font-light mb-2">{dest.name}</div>
                  <div className="text-sm opacity-70 mb-sm">{dest.duration}</div>
                  {dest.price && (
                    <div className="text-lg font-medium /* TODO: Replace with CardTitle */">
                      {dest.price}
                    </div>
                  )}
                  {selectedDestination === dest.id && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute top-4 right-4 w-3 h-3 bg-sanctuary"
                    />
                  )}
                </div>
              </motion.a>
            ))}
          </motion.div>

          {/* Enhanced Current Destination Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative overflow-hidden  bg-gradient-to-br from-sanctuary/90 to-warm-sanctuary/90 backdrop-blur-lg p-12 shadow-2xl border border-terra-lotus/10"
          >
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-golden-lotus/5 via-transparent to-temple/5" />
            
            <div className="relative z-10 grid md:grid-cols-2 gap-xl items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <h2 className="heading-section text-charcoal mb-md">{currentDestination.name}</h2>
                <p className="text-lg text-charcoal/80 mb-lg leading-relaxed">
                  {currentDestination.description}
                </p>
                
                {/* Highlights */}
                <div className="flex flex-wrap gap-3 mb-lg">
                  {currentDestination.highlights.map((highlight, index) => (
                    <motion.span
                      key={highlight}
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                      className="px-container-sm py-2 bg-gradient-to-r from-golden-lotus/10 to-temple/10 text-terra-lotus text-sm font-medium border border-terra-lotus/20"
                    >
                      {highlight}
                    </motion.span>
                  ))}
                </div>
                
                {/* Price */}
                <motion.div
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 1 }}
                  className="text-2xl font-primary text-terra-lotus font-light"
                >
                  {currentDestination.priceRange}
                </motion.div>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
                className="relative h-80 overflow-hidden group"
              >
                <Image
                  src={currentDestination.image}
                  alt={`Retreat jogowy ${currentDestination.name}`}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-charcoal/20 via-transparent to-transparent" />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Daily Program */}
      <section className="py-section px-container-sm sm:px-hero-padding lg:px-hero-padding bg-gradient-to-b from-warm-sanctuary to-sanctuary">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <div className="w-24 h-px bg-terra-lotus/40 mx-auto mb-lg"></div>
            <h2 className="heading-section text-charcoal mb-lg">
              Program Dzień po Dień
            </h2>
            <p className="text-lg text-charcoal/70 max-w-3xl mx-auto leading-relaxed">
              Szczegółowy harmonogram {currentDestination.duration} retreatu jogowego na {currentDestination.name}
            </p>
          </motion.div>

          <div className="space-y-16">
            {programDays.map((day, index) => (
              <motion.div
                key={day.day}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="relative overflow-hidden  bg-gradient-to-br from-sanctuary/80 to-warm-sanctuary/80 backdrop-blur-lg shadow-xl border border-terra-lotus/10"
              >
                {/* Background decoration */}
                <div className="absolute inset-0 bg-gradient-to-br from-golden-lotus/3 via-transparent to-temple/3" />
                
                <div className="relative z-10 p-8 md:p-12">
                  <div className={`md:flex items-start gap-2xl ${index % 2 === 1 ? 'md:flex-row-reverse' : ''}`}>
                    {/* Enhanced Image Section */}
                    <motion.div
                      initial={{ opacity: 0, x: index % 2 === 1 ? 30 : -30 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.8, delay: 0.3 }}
                      className="md:w-2/5 relative h-64 md:h-80 mb-lg md:mb-0 overflow-hidden group"
                    >
                      {day.image ? (
                        <Image
                          src={day.image}
                          alt={`Dzień ${day.day} - ${day.title}`}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                          sizes="(max-width: 768px) 100vw, 40vw"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-golden-lotus/10 to-temple/10 flex items-center justify-center">
                          <span className="text-terra-lotus text-4xl font-primary font-light /* TODO: Replace with HeroTitle */">
                            Dzień {day.day}
                          </span>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-charcoal/20 via-transparent to-transparent" />
                      
                      {/* Day number overlay */}
                      <div className="absolute top-4 left-4 bg-terra-lotus/90 text-sanctuary px-3 py-1 text-sm font-medium">
                        Dzień {day.day}
                      </div>
                    </motion.div>

                    {/* Enhanced Content Section */}
                    <motion.div
                      initial={{ opacity: 0, x: index % 2 === 1 ? -30 : 30 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.8, delay: 0.4 }}
                      className="md:w-3/5"
                    >
                      <div className="mb-lg">
                        <h3 className="text-3xl font-primary text-charcoal leading-tight mb-sm font-light /* TODO: Replace with SectionTitle */" /* TODO: Replace with CardTitle */>
                          {day.title}
                        </h3>
                        {day.subtitle && (
                          <p className="text-lg text-charcoal/60 italic">
                            {day.subtitle}
                          </p>
                        )}
                      </div>

                      <div className="space-y-sm">
                        {day.activities.map((activity, actIndex) => (
                          <motion.div
                            key={actIndex}
                            initial={{ opacity: 0, x: 20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.6, delay: 0.5 + actIndex * 0.1 }}
                            className="flex items-start gap-sm p-4 bg-sanctuary/50 backdrop-blur-sm border border-terra-lotus/10 hover:bg-terra-lotus/5 transition-colors duration-300"
                          >
                            <div className="w-2 h-2 bg-terra-lotus mt-3 flex-shrink-0" />
                            <span className="text-charcoal/80 leading-relaxed">
                              {activity}
                            </span>
                          </motion.div>
                        ))}
                      </div>

                      {/* Additional info if available */}
                      {day.note && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          transition={{ duration: 0.8, delay: 0.8 }}
                          className="mt-md p-4 bg-charcoal/5 border-l-4 border-charcoal"
                        >
                          <p className="text-charcoal/70 italic text-sm">
                            {day.note}
                          </p>
                        </motion.div>
                      )}
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced What's Included */}
      <section className="py-section px-container-sm sm:px-hero-padding lg:px-hero-padding bg-gradient-to-b from-sanctuary to-warm-sanctuary">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <div className="w-24 h-px bg-terra-lotus/40 mx-auto mb-lg"></div>
            <h2 className="heading-section text-charcoal mb-lg">
              Szczegóły Pakietu
            </h2>
            <p className="text-lg text-charcoal/70 max-w-3xl mx-auto leading-relaxed">
              Przejrzysta struktura kosztów - dokładnie wiemy co zawiera cena retreatu
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-lg max-w-6xl mx-auto">
            {/* Enhanced Included Services */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative overflow-hidden  bg-gradient-to-br from-sanctuary/90 to-warm-sanctuary/90 backdrop-blur-lg p-8 shadow-xl border border-sage/20"
            >
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-sage/5 via-transparent to-sage/10" />
              
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-lg">
                  <div className="w-8 h-8 bg-sage flex items-center justify-center">
                    <svg className="w-4 h-4 text-sanctuary" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                    </svg>
                  </div>
                  <h3 className="text-2xl font-primary text-charcoal font-light" /* TODO: Replace with CardTitle */>
                    W cenie zawarte
                  </h3>
                </div>

                <div className="space-y-3">
                  {includedServices.map((service, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                      className="flex items-start gap-sm p-4 bg-sanctuary/50 backdrop-blur-sm border border-sage/10 hover:bg-sage/5 transition-colors duration-300"
                    >
                      <div className="w-2 h-2 bg-sage mt-3 flex-shrink-0" />
                      <span className="text-charcoal/80 leading-relaxed">
                        {service}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Enhanced Additional Costs */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="relative overflow-hidden  bg-gradient-to-br from-sanctuary/90 to-warm-sanctuary/90 backdrop-blur-lg p-8 shadow-xl border border-terra-lotus/20"
            >
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-br from-golden-lotus/5 via-transparent to-temple/10" />
              
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-lg">
                  <div className="w-8 h-8 bg-terra-lotus flex items-center justify-center">
                    <svg className="w-4 h-4 text-sanctuary" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                  <h3 className="text-2xl font-primary text-charcoal font-light" /* TODO: Replace with CardTitle */>
                    Koszty dodatkowe
                  </h3>
                </div>

                <div className="space-y-3">
                  {additionalCosts.map((cost, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                      className="flex items-start gap-sm p-4 bg-sanctuary/50 backdrop-blur-sm border border-terra-lotus/10 hover:bg-terra-lotus/5 transition-colors duration-300"
                    >
                      <div className="w-2 h-2 bg-terra-lotus mt-3 flex-shrink-0" />
                      <span className="text-charcoal/80 leading-relaxed">
                        {cost}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Enhanced Important Info Note */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-xl p-6 bg-charcoal/10 border border-charcoal/20 max-w-4xl mx-auto"
          >
            <div className="text-center">
              <div className="text-charcoal mb-2">💡</div>
              <p className="text-charcoal/70 leading-relaxed">
                <strong>Wskazówka:</strong> Dodatkowe koszty to orientacyjne wartości, które pomogą Ci zaplanować budżet na podróż. 
                Wszystkie ceny w pakiecie są ustalone z góry - żadnych ukrytych kosztów!
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Important Info */}
      <section className="py-section px-container-sm sm:px-hero-padding lg:px-hero-padding bg-gradient-to-b from-warm-sanctuary to-sanctuary">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <div className="w-24 h-px bg-terra-lotus/40 mx-auto mb-lg"></div>
            <h2 className="heading-section text-charcoal mb-lg">
              Ważne Informacje
            </h2>
            <p className="text-lg text-charcoal/70 max-w-3xl mx-auto leading-relaxed">
              Wszystko co musisz wiedzieć przed wyruszeniem w duchową podróż
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-md mb-2xl">
            {[
              {
                icon: "🧘‍♀️",
                title: "Poziom trudności",
                description: "Program dostosowany do wszystkich poziomów zaawansowania. Każdy znajdzie coś dla siebie - od początkujących po zaawansowanych joginów."
              },
              {
                icon: "👥",
                title: "Rozmiar grupy",
                description: "Maksymalnie 12 osób, co gwarantuje indywidualne podejście i kameralną atmosferę podczas całego retreatu."
              },
              {
                icon: "🎒",
                title: "Wyposażenie",
                description: "Maty do jogi zapewnione na miejscu. Zalecamy zabranie własnej odzieży sportowej i wygodnych butów."
              },
              {
                icon: "🌤️",
                title: "Pogoda",
                description: `${currentDestination.name} ma klimat tropikalny - ciepło przez cały rok (26-30°C). Możliwe krótkie opady deszczu.`
              }
            ].map((info, index) => (
              <motion.div
                key={info.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="relative overflow-hidden bg-gradient-to-br from-sanctuary/90 to-warm-sanctuary/90 backdrop-blur-lg p-6 shadow-xl border border-terra-lotus/10 hover:shadow-2xl transition-shadow duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-golden-lotus/3 via-transparent to-temple/5" />
                
                <div className="relative z-10 text-center">
                  <div className="text-3xl mb-sm">{info.icon}</div>
                  <h3 className="text-xl font-primary text-charcoal mb-sm font-light">
                    {info.title}
                  </h3>
                  <p className="text-charcoal/70 leading-relaxed text-sm">
                    {info.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Call to Action */}
      <section className="py-section px-container-sm sm:px-hero-padding lg:px-hero-padding bg-gradient-to-br from-sanctuary via-warm-sanctuary to-sanctuary">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="relative overflow-hidden  bg-gradient-to-br from-golden-lotus/10 via-sanctuary/90 to-temple/10 backdrop-blur-xl p-12 shadow-2xl border border-terra-lotus/20"
          >
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-golden-lotus/5 via-transparent to-temple/5" />
            
            {/* Floating elements */}
            <div className="absolute top-6 right-6 w-24 h-24 bg-terra-lotus/10 blur-xl animate-pulse" />
            <div className="absolute bottom-6 left-6 w-32 h-32 bg-charcoal/10 blur-xl animate-pulse" style={{ animationDelay: '1s' }} />
            
            <div className="relative z-10 text-center">
              {/* Om Symbol */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-terra-lotus mb-lg"
              >
                <svg className="w-12 h-12 mx-auto" viewBox="0 0 100 100" fill="currentColor">
                  <path d="M50 20c-5.5 0-10 4.5-10 10s4.5 10 10 10 10-4.5 10-10-4.5-10-10-10zm0 25c-8.3 0-15 6.7-15 15s6.7 15 15 15 15-6.7 15-15-6.7-15-15-15zm0 35c-11 0-20-9-20-20s9-20 20-20 20 9 20 20-9 20-20 20z" />
                </svg>
              </motion.div>

              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-4xl font-primary text-charcoal mb-md font-light"
              >
                Gotowa na Transformację?
              </motion.h3>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-xl text-charcoal/80 leading-relaxed mb-10 max-w-2xl mx-auto"
              >
                Dołącz do naszego wyjątkowego retreatu na {currentDestination.name}. 
                Odkryj magię jogi w tropikalnym raju i wróć do domu z nową energią i perspektywą.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-md justify-center items-center"
              >
                <motion.a
                  href="/rezerwacja"
                  className="btn-primary text-lg px-hero-padding py-4 group"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    if (window.trackRetreatBooking) {
                      window.trackRetreatBooking(currentDestination.name, 'cta_booking', 1);
                    }
                  }}
                >
                  Rezerwuj Swoje Miejsce
                  <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </motion.a>

                <span className="text-charcoal/40 text-lg">lub</span>

                <motion.a
                  href="/kontakt"
                  className="btn-secondary text-lg px-hero-padding py-4 group"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    if (window.trackContactInterest) {
                      window.trackContactInterest('program_page', {
                        destination: currentDestination.name,
                        action: 'ask_availability'
                      });
                    }
                  }}
                >
                  Zapytaj o Dostępność
                  <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </motion.a>
              </motion.div>

              {/* Trust indicators */}
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mt-lg flex flex-wrap justify-center items-center gap-md text-sm text-charcoal/60"
              >
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-sage" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                  </svg>
                  <span>Certyfikowana instruktorka</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-sage" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                  </svg>
                  <span>Małe grupy (max 12 osób)</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-sage" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                  </svg>
                  <span>Wsparcie 24/7</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* WhatsApp Button jest już dostępny globalnie poprzez QuickCTA */}
    </main>
  );
}